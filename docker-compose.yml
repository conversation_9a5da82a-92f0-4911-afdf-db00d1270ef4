services:
  synapse:
    image: docker.io/matrixdotorg/synapse:latest
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - SYNAPSE_DATABASE_HOST=${POSTGRES_HOST}
      - SYNAPSE_DATABASE_PORT=${POSTGRES_PORT}
      - SYNAPSE_DATABASE_USER=${POSTGRES_USER}
      - SYNAPSE_DATABASE_PASSWORD=${POSTGRES_PASSWORD}
      - SYNAPSE_DATABASE_NAME=${POSTGRES_DB}
    volumes:
      - ${SYNAPSE_DATA_PATH}:/data
      - ./synapse_entrypoint.sh:/synapse_entrypoint.sh:ro
    entrypoint: /synapse_entrypoint.sh
    depends_on:
      - db
    networks:
      - matrix-internal
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8008/_matrix/client/versions"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
  db:
    image: docker.io/postgres:15-alpine
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - ${POSTGRES_DATA_PATH}:/var/lib/postgresql/data
    networks:
      - matrix-internal
  element:
    image: vectorim/element-web:latest
    restart: unless-stopped
    volumes:
      - ${ELEMENT_CONFIG_PATH}:/app/config.json:ro
    networks:
      - matrix-internal
  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - ${NGINX_HTTP_PORT}:80
    volumes:
      - ${NGINX_CONFIG_PATH}:/etc/nginx/conf.d/default.conf:ro
    networks:
      - matrix-internal
    depends_on:
      - synapse
      - element
    labels:
      - "homepage.group=Communication"
      - "homepage.name=Matrix Homeserver"
      - "homepage.icon=si-matrix"
      - "homepage.href=http://matrix.oculair.ca"
      - "homepage.description=Matrix homeserver with Element web client"
  matrix-client:
    build:
      context: .
      dockerfile: Dockerfile.matrix-client
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - MATRIX_HOMESERVER_URL=http://synapse:8008
    volumes:
      - ./matrix_store:/app/matrix_store
      - ./matrix_client_data:/app/data
    networks:
      - matrix-internal
    depends_on:
      synapse:
        condition: service_healthy
      matrix-api:
        condition: service_started
      mcp-server:
        condition: service_started
    healthcheck:
      test:
        - CMD
        - python
        - -c
        - import sys; sys.exit(0)
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
  matrix-api:
    build:
      context: .
      dockerfile: Dockerfile.matrix-api
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - MATRIX_HOMESERVER_URL=http://synapse:8008
    ports:
      - 8004:8000
    networks:
      - matrix-internal
    depends_on:
      - synapse
    labels:
      - "homepage.group=Communication"
      - "homepage.name=Matrix API"
      - "homepage.icon=si-api"
      - "homepage.href=http://*************:8004"
      - "homepage.description=Matrix API server for automation"
  mcp-server:
    build:
      context: .
      dockerfile: Dockerfile.mcp-server
    restart: unless-stopped
    env_file:
      - .env
    environment:
      - MATRIX_API_URL=http://matrix-api:8000
      - MATRIX_HOMESERVER_URL=http://synapse:8008
      - MCP_HOST=0.0.0.0
      - MCP_PORT=8005
      - MCP_HTTP_HOST=0.0.0.0
      - MCP_HTTP_PORT=8006
    ports:
      - 8015:8005 # WebSocket port (host:container)
      - 8016:8006 # HTTP streaming port (host:container)
    networks:
      - matrix-internal
    depends_on:
      - synapse
      - matrix-api
    volumes:
      - ./mcp_data:/app/data
    labels:
      - "homepage.group=Communication"
      - "homepage.name=Matrix MCP Server"
      - "homepage.icon=si-websocket"
      - "homepage.href=http://*************:8016"
      - "homepage.description=Matrix MCP server for Claude integration"
networks:
  matrix-internal:
    driver: bridge
