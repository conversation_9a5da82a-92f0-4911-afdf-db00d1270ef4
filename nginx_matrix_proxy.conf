server {
    # Nginx inside the container will listen on port 80.
    # <PERSON><PERSON> Compose will map NGINX_HTTP_PORT from .env to this port.
    listen 80 default_server;
    listen [::]:80 default_server;

    # Accept requests from any hostname when behind Cloudflare
    server_name matrix.oculair.ca _;

    # Access and error logs (optional, paths depend on your Nginx container setup)
    # access_log /var/log/nginx/matrix.access.log;
    # error_log /var/log/nginx/matrix.error.log;

    # Cloudflare real IP restoration
    set_real_ip_from ************/20;
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from *************/18;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from **********/13;
    set_real_ip_from **********/22;
    set_real_ip_from 2400:cb00::/32;
    set_real_ip_from 2606:4700::/32;
    set_real_ip_from 2803:f800::/32;
    set_real_ip_from 2405:b500::/32;
    set_real_ip_from 2405:8100::/32;
    set_real_ip_from 2a06:98c0::/29;
    set_real_ip_from 2c0f:f248::/32;
    real_ip_header CF-Connecting-IP;

    # Set scheme based on Cloudflare header
    set $proto $scheme;
    if ($http_x_forwarded_proto) {
        set $proto $http_x_forwarded_proto;
    }

    location ~ ^(/_matrix|/_synapse/client|/_synapse/admin) {
        proxy_pass http://synapse:8008; # Synapse service name
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $proto;
        proxy_set_header Host $http_host; # Use $http_host to pass the original host header
        
        client_max_body_size 50M;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_buffering off;
    }

    location / {
        proxy_pass http://element:80; # Element service name
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto https;
        proxy_set_header Host $http_host;

        client_max_body_size 50M;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
}