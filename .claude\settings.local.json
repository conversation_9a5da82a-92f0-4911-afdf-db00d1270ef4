{"permissions": {"allow": ["Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose ps:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(sudo lsof:*)", "Bash(docker-compose build:*)", "Bash(docker logs:*)", "Bash(find:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker restart:*)", "<PERSON><PERSON>(docker stop:*)", "<PERSON><PERSON>(docker rm:*)", "<PERSON><PERSON>(docker start:*)", "<PERSON><PERSON>(docker:*)", "<PERSON><PERSON>(python test:*)", "<PERSON><PERSON>(python3:*)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(git add:*)", "Bash(git push:*)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "WebFetch(domain:docs.letta.com)", "<PERSON><PERSON>(cat:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git commit:*)", "Bash(ls:*)", "Bash(sudo chown:*)", "<PERSON><PERSON>(true)", "Bash(git config:*)"], "deny": []}}