{"default_server_config": {"m.homeserver": {"base_url": "https://matrix.oculair.ca/"}, "m.identity_server": {"base_url": "https://vector.im"}}, "brand": "Element", "integrations_ui_url": "https://scalar.vector.im/", "integrations_rest_url": "https://scalar.vector.im/api", "integrations_widgets_urls": ["https://scalar.vector.im/_matrix/integrations/v1", "https://scalar.vector.im/api", "https://scalar-staging.vector.im/_matrix/integrations/v1", "https://scalar-staging.vector.im/api", "https://scalar-staging.riot.im/scalar/api"], "bug_report_endpoint_url": "https://element.io/bugreports/submit", "uisi_autorageshake_app": "element-auto-uisi", "showLabsSettings": true, "roomDirectory": {"servers": ["matrix.org", "gitter.im", "libera.chat"]}, "enable_presence_by_hs_url": {"https://matrix.org": false, "https://matrix-client.matrix.org": false}, "terms_and_conditions_links": [{"url": "https://element.io/privacy", "text": "Privacy Policy"}, {"url": "https://element.io/cookie-policy", "text": "<PERSON><PERSON>"}], "sentry": {"dsn": "https://<EMAIL>/6", "environment": "develop"}, "posthog": {"projectApiKey": "phc_Jzsm6DTm6V2705zeU5dcNvQDlonOR68XvX2sh1sEOHO", "apiHost": "https://posthog.element.io"}, "privacy_policy_url": "https://element.io/cookie-policy", "features": {"feature_spotlight": true, "feature_video_rooms": true}, "element_call": {"url": "https://element-call.netlify.app"}, "map_style_url": "https://api.maptiler.com/maps/streets/style.json?key=fU3vlMsMn4Jb6dnEIFsx"}