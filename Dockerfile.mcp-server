# MCP Server Dockerfile
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Create data directory for file operations
RUN mkdir -p /app/data

# Copy application files
COPY mcp_server.py .
COPY mcp_http_server.py .

# Install Python dependencies directly (no requirements.txt needed for now)
RUN pip install --no-cache-dir \
    websockets==12.0 \
    aiohttp==3.9.1 \
    python-dotenv==1.0.0

# Create non-root user for security
RUN useradd -m -u 1000 mcpserver && chown -R mcpserver:mcpserver /app
USER mcpserver

# Expose both WebSocket and HTTP ports
EXPOSE 8005 8006

# Health check for HTTP server
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import urllib.request; urllib.request.urlopen('http://localhost:8006/health')" || exit 1

# Start the MCP HTTP server (can switch to websocket server by changing the command)
CMD ["python", "mcp_http_server.py"]