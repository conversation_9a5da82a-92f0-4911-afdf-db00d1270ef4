# PostgreSQL Settings
POSTGRES_HOST=db
POSTGRES_PORT=5432
POSTGRES_USER=synapse
POSTGRES_PASSWORD=your_secure_password_here
POSTGRES_DB=synapse

# Synapse Settings
SYNAPSE_SERVER_NAME=your.matrix.domain
SYNAPSE_REPORT_STATS=no
SYNAPSE_DATA_PATH=./synapse-data

# Data Paths
POSTGRES_DATA_PATH=./postgres-data

# Config Paths
ELEMENT_CONFIG_PATH=./element-config.json
NGINX_CONFIG_PATH=./nginx_matrix_proxy.conf

# Nginx Settings
NGINX_HTTP_PORT=8008

# Matrix Client Settings
MATRIX_HOMESERVER_URL=http://synapse:8008
MATRIX_USERNAME=@your_bot_user:your.matrix.domain
MATRIX_PASSWORD=your_bot_password
MATRIX_ROOM_ID=!your_room_id:your.matrix.domain
LETTA_API_URL=https://your.letta.api.url
LETTA_TOKEN=your_letta_token
LETTA_AGENT_ID=your_letta_agent_id

# MCP Server Settings
MCP_HOST=0.0.0.0
MCP_PORT=8005
MCP_SERVER_URL=ws://mcp-server:8005

# Matrix MCP Bridge Settings
MATRIX_MCP_USERNAME=@mcp-bot:your.matrix.domain
MATRIX_MCP_PASSWORD=mcp_bot_password
MATRIX_MCP_ALLOWED_ROOMS=